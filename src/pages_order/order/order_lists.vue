<template>
  <view :style="theme.style" class="user-order">
    <view class="top-warp">
      <u-tabs
        :list="tabs"
        :current="current"
        :line-color="variables.colorPrimary"
        :active-style="{ fontWeight: 500, color: '#101010' }"
        :scrollable="false"
        @change="tabsChange"
      ></u-tabs>
      <!-- 塞选条件 -->
      <view ref="dropdownRef" class="bg-white plug-election" style="margin-bottom: 10rpx" :key="type">
        <dropdown menu-icon="arrow-down-fill" menu-icon-size="10rpx" @open="changeDropdown(true)" @close="changeDropdown(false)">
          <!-- <template> -->
          <dropdown-item
            v-model="tableDropdownData.valueDate"
            :title="tableDropdownData.valueDateTitle"
            :options="optionsDate"
            @change="changeMenuEvent($event, 'date')"
          ></dropdown-item>
          <dropdown-item
            v-model="tableDropdownData.valueItems"
            v-if="showProjectPoint && !isAddressVisitor"
            :title="tableDropdownData.valueItemsTitle"
            :options="optionsItems"
            @change="changeMenuEvent($event, 'items')"
          ></dropdown-item>
          <dropdown-item
            v-model="tableDropdownData.valuePay"
            :title="tableDropdownData.valuePayTitle"
            :options="optionsPay"
            @change="changeMenuEvent($event, 'pay')"
          ></dropdown-item>
          <dropdown-item
            v-model="tableDropdownData.valueConsumer"
            v-if="showProjectPoint && !isAddressVisitor"
            :title="tableDropdownData.valueConsumerTitle"
            :options="optionsConsumer"
            @change="changeMenuEvent($event, 'consumer')"
          ></dropdown-item>
          <!--判断 0  1 消费 退款 tab-->
          <dropdown-item
            v-if="type == '0' || type == '1'"
            v-model="tableDropdownData.totalPaymentOrderType"
            :title="tableDropdownData.totalPaymentOrderTitle"
            :options="totalPaymentOrderTypeList"
            @change="changeMenuEvent($event, 'totalPaymentOrderType')"
          ></dropdown-item>
          
          <!-- </template> -->
        </dropdown>
      </view>
    </view>

    <!-- top="xxx"下拉布局往下偏移,防止被悬浮菜单遮住 -->
    <mescroll-uni
      ref="mescrollRef"
      @init="mescrollInit"
      top="210"
      @down="downCallback"
      :up="upOption"
      @up="upCallback"
      @emptyclick="emptyClick"
    >
      <!-- 数据列表 -->
      <order-lists
        v-if="results && results.length"
        :results="results"
        :start-time="startTime"
        :end-time="endTime"
        @finishDown="finishDown"
        @untie='untie'
        @updateList="updateList">
      </order-lists>
    </mescroll-uni>
    <!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
    <!-- <view class="form-btn">
      <u-button
        @click="gotoInvoiceSelectOrder"
        text="开发票"
        shape="circle"
        color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)"
      ></u-button>
    </view> -->
    <!-- 自定义日期 -->
    <u-calendar :show="show" round mode="range" @confirm="confirmCalendar"></u-calendar>
  </view>
</template>

<script>
import orderLists from '../components/order/lists.vue'
// import rechargeItem from "../components/order/recharge/item.vue"
// import consumeItem from "../components/order/consume/item.vue"
// import withdrawalItem from "../components/order/withdrawal/item.vue"
// import refundItem from "../components/order/refund/item.vue"
import { getApiUserGetProjectCardUserList } from '@/api/app'
import { getApiUserOrderUserOrderList, apiBookingUserOrderReleaseOrder } from '@/api/order'
import { getApiorBuffOeterPaying } from '@/api/buffet'
import { mapGetters } from 'vuex'
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'

import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
import { uniqueArrKey } from '../../utils/util'
export default {
  mixins: [MescrollMixin],
  components: {
    orderLists,
    // rechargeItem,
    // consumeItem,
    // withdrawalItem,
    // refundItem
    FloatingPopup
  },
  data() {
    return {
      results: [],
      type: '',
      current: 0,
      tabs: [
        {
          name: '全部',
          type: ''
        },
        {
          name: '消费',
          type: '0'
        },
        {
          name: '充值',
          type: '3'
        },
        {
          name: '提现',
          type: '2'
        },
        {
          name: '退款',
          type: '1'
        }
      ],
      openDropdown: false, // 是否打开Dropdown
      tableDropdownData: {
        valueItems: '', //项目点
        valuePay: '',
        valueConsumer: '', // 消费人
				totalPaymentOrderType:"",
        valueDate: '1个月', //日期
        valueItemsTitle: '全部项目',
        valueConsumerTitle: '消费人',
        valueDateTitle: '1个月内',
        valuePayTitle: '支付状态',
				totalPaymentOrderTitle:"全部",
      },
      optionsItems: [], //选择项目
      optionsPay: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '成功',
          value: 'ORDER_SUCCESS'
        },
        {
          label: '退款中',
          value: 'ORDER_REFUNDING'
        },
        {
          label: '待支付',
          value: 'ORDER_PAYING'
        },
        {
          label: '失败',
          value: 'ORDER_FAILED'
        }
      ],
			totalPaymentOrderTypeList:[
        {
          label: '全部',
          value: ''
        },
        {
          label: '预约订单',
          value: 'reservation'
        },
        {
          label: '堂食订单',
          value: 'on_scene'
        },
			],
      optionsConsumer: [],
      optionsDate: [
        {
          label: '全部时间',
          value: '全部时间',
          start_time: uni.$u.timeFormat(new Date('2021/01/01').getTime(), 'yyyy-mm-dd 00:00:00'),
          end_time: uni.$u.timeFormat(this.getDayEndTime(), 'yyyy-mm-dd hh:MM:ss')
        },
        {
          label: '今天1',
          value: '今天',
          start_time: uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd 00:00:00'),
          end_time: uni.$u.timeFormat(this.getDayEndTime(), 'yyyy-mm-dd hh:MM:ss')
        },
        {
          label: '3天内',
          value: '3天',
          start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 2, 'yyyy-mm-dd 00:00:00'),
          end_time: uni.$u.timeFormat(this.getDayEndTime(), 'yyyy-mm-dd hh:MM:ss')
        },
        {
          label: '1周内',
          value: '1周',
          start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 7, 'yyyy-mm-dd 00:00:00'),
          end_time: uni.$u.timeFormat(this.getDayEndTime(), 'yyyy-mm-dd hh:MM:ss')
        },
        {
          label: '1个月内',
          value: '1个月',
          start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 30, 'yyyy-mm-dd 00:00:00'),
          end_time: uni.$u.timeFormat(this.getDayEndTime(), 'yyyy-mm-dd hh:MM:ss')
        },
        {
          label: '3个月内',
          value: '3个月',
          start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 90, 'yyyy-mm-dd hh:MM:ss'),
          end_time: uni.$u.timeFormat(this.getDayEndTime(), 'yyyy-mm-dd 00:00:00')
        },
        {
          label: '自定义日期',
          value: '自定义日期',
          type: 'custom',
          start_time: '',
          end_time: ''
        }
      ],
      startTime: uni.$u.timeFormat(new Date().getTime() - 86400000 * 30, 'yyyy-mm-dd 00:00:00'),
      endTime: uni.$u.timeFormat(this.getDayEndTime(), 'yyyy-mm-dd hh:MM:ss'),
      upOption: {},
      showProjectPoint: false, // 显示项目点的筛选
      canReset: false,
      floatingPopupShow: false
    }
  },
  mounted() {
    // #ifdef H5
    this.$refs.dropdownRef.$el.addEventListener('touchmove', this.preventDropdown, true)
    // #endif
  },
  beforeDestroy() {
    // #ifdef H5
    this.$refs.dropdownRef.$el.removeEventListener('touchmove', this.preventDropdown, true)
    // #endif
  },
  computed: {
		...mapGetters(['isAddressVisitor'])
	},
  methods: {
    // 禁止dropdown打开时下拉
    preventDropdown(e) {
      if (this.openDropdown) e.preventDefault()
    },
    tabsChange(e) {
			 this.tableDropdownData.totalPaymentOrderType = ""
			 this.tableDropdownData.totalPaymentOrderTitle = "全部"
      this.results = []
      this.type = e.type
      this.upCallback({ num: 1, size: 10 })
    },
    changeMenuEvent(e, type) {
      if (type == 'items') {
        let cardUserObj = {}
        this.tableDropdownData.valueConsumerTitle = '消费人'
        this.tableDropdownData.valueConsumer = ''
        this.optionsConsumer = []
        // 根据项目点找到对应点消费人
        this.optionsItems.map(v => {
          if (v.company_id == e) {
            this.tableDropdownData.valueItemsTitle = v.company_name
            cardUserObj = {
              label: v.name,
              value: v.person_no
            }
            this.optionsConsumer.push(cardUserObj)
          }
        })
        this.optionsConsumer.push({
          label: '只看游客',
          value: 'is_visitor'
        })
      } else if (type == 'date') {
        this.optionsDate.map(v => {
          if (v.value == e) {
            this.startTime = v.start_time
            this.endTime = v.end_time
            this.tableDropdownData.valueDateTitle = e
          }
        })
      } else if (type == 'consumer') {
        this.tableDropdownData.valueConsumerTitle = this.optionsConsumer.find(n => n.value == e).label
      } else if (type == 'pay') {
        this.tableDropdownData.valuePayTitle = this.optionsPay.find(n => n.value == e).label
        // this.tableDropdownData.valueConsumerTitle = this.optionsConsumer.find((n) => n.value == e).label
      }else if (type == 'totalPaymentOrderType'){
				this.tableDropdownData.totalPaymentOrderTitle = this.totalPaymentOrderTypeList.find(n => n.value == e).label
			}
      this.upCallback({ num: 1, size: 10 })
    },
    // this.current = index
    getUserGetProjectCardUserList() {
      getApiUserGetProjectCardUserList()
        .then(res => {
          if (res.code == 0) {
            let obj = {}
            this.optionsItems = []
            res.data.map(v => {
              // push 项目点
              obj = {
                label: v.company_name,
                value: v.company_id,
                ...v
              }
              this.optionsItems.push(obj)
            })
            // this.itemsData = res.data
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    changeDropdown(data) {
      this.openDropdown = data
      if (data) {
        this.mescroll.optUp.isLock = true
        this.mescroll.optDown.isLock = true
      } else {
        this.mescroll.lockUpScroll(false)
        this.mescroll.lockDownScroll(false)
      }
    },
    upCallback(page) {
      // setTimeout(() => {
      // 	this.mescroll.endBySize(1, 1)
      // }, 500)
      if (this.openDropdown) {
        // 当筛选框dropdown弹出时禁止触发拉取数据
        return
      }
      this.getUserOrderUserOrderList(page)
    },
    getUserOrderUserOrderList(page) {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      let params = {
        page: page.num,
        page_size: page.size,
        start_time: this.startTime,
        end_time: this.endTime,
        order_status: this.tableDropdownData.valuePay,
        company_id: !this.showProjectPoint ? this.$store.getters.userInfo.company_id : this.isAddressVisitor ? uni.getStorageSync('companyId') : this.tableDropdownData.valueItems,
        person_no: !this.showProjectPoint ? this.$store.getters.userInfo.person_no : this.tableDropdownData.valueConsumer,
        order_type: this.type,
				total_payment_order_type: this.tableDropdownData.totalPaymentOrderType,
      }
      if (this.tableDropdownData.valueConsumer === 'is_visitor' || this.isAddressVisitor) {
        params.is_visitor = true
        params.person_no = ''
        params.openid = uni.getStorageSync('codeOpenid') || this.$store.getters.userInfo.wx_open_id
      }
      getApiUserOrderUserOrderList(this.$deleteEmptyKey(params))
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            console.log('查看订单', res);
            // 取消 称重绑盘
            // uni.setStorageSync('needBindtray', 'false')
            const results = res.data.results
            const count = res.data.count
            let pageLength = 0
            if (results) { 
              pageLength = results.length 
            } else {
              pageLength = 0
            }
            // 如果是第一页需手动置空列表
            if (page.num == 1) this.results = []
            // 追加新数据
            let newList = [...this.results, ...results]
            //方法二(推荐): 后台接口有返回列表的总数据量 count, 判断是否有下一页
            this.mescroll.endBySize(pageLength, count)
            this.results = uniqueArrKey(newList, 'trade_no')
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.hideLoading()
          // uni.$u.toast(error.message)
        })
    },
    // 称重倒计时结束
    finishDown(data) {
      this.getorBuffOeterPaying(data.id)
    },
    // 称重支付
    getorBuffOeterPaying(id) {
      this.$showLoading({
        title: '支付中....',
        mask: true
      })
      let params = { order_id: id }
      getApiorBuffOeterPaying(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.upCallback({ num: 1, size: 10 })
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    gotoInvoiceSelectOrder() {
      this.$miRouter.push({
        query: {},
        path: '/pages_order/invoice/select_order'
      })
    },
    // 刷新列表
    updateList(){
      this.upCallback({ num: 1, size: 10 })
    },
    // 获取当天末尾时间
    getDayEndTime() {
      var times = new Date()
      times.setHours(23)
      times.setMinutes(59)
      times.setSeconds(59)
      return times.getTime()
    },
    untie(e) {
      let params = {
        order_payment_id: e.order_payment_id
      }
      apiBookingUserOrderReleaseOrder(params)
      .then((res) => {
        if (res.code === 0) {
          this.updateList()
          uni.$u.toast('解绑成功')
        } else {
          uni.$u.toast(res.msg)
        }
      })
    }
  },
  onLoad(options) {
    if (options.project_point) {
      // create: 2022-03-01 14:33:00 bug 当两个页面都跳这里会出现showProjectPoint赋值上了但页面没更新，谜之暂时没想到如何解决
      // uni-simple-router 2.0.8-beta.2 中出现的bug

      // create: 2022-03-01 15:48:00 fix: 升级uni-simple-router 2.0.8-beta.3 解决
      this.showProjectPoint = !!Number(options.project_point)
    }
  },
  onShow() {
    // 申诉成功的话,刷新下页面
    this.$eventbus.$on('reloadOrder', val => {
      this.canReset = val
    })
    this.canReset && this.mescroll.resetUpScroll() // 重置列表数据为第一页
    this.canReset && this.mescroll.scrollTo(0, 0) // 重置列表数据为第一页时,建议把滚动条也重置到顶部,避免无法再次翻页的问题
    this.canReset = false
    this.getUserGetProjectCardUserList()
    this.floatingPopupShow = !this.floatingPopupShow
  }
}
</script>

<style lang="scss">
.user-order {
  // height: 100vh;
  height: 100%;
  .top-warp {
    z-index: 20;
    position: fixed;
    top: --window-top; /* css变量 */
    left: 0;
    width: 100%;
    height: 98upx;
    background-color: white;
  }
  .plug-election {
    // padding-top: 100rpx;
    // height: 100%;
    border-top: $border-base;
    padding: 10rpx 0;
  }
  .tabs-content {
    min-height: 0;
  }
  .form-btn{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx 40rpx;
    background: #fff;
    z-index: 9999;
  }
}
.hidden-order {
  overflow: hidden;
  height: 100vh;
}
</style>
